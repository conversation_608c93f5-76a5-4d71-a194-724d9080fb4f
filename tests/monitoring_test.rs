use std::time::Duration;
use tokio::time::sleep;
use chrono::Utc;

use kvm_tunnel::config::{MonitoringConfig, AlertConfig, AlertThresholds, LoggingConfig, LogFormat, LogFileConfig};
use kvm_tunnel::monitoring::{MonitoringManager, logging::{LogManager, LogEntry, LogLevel}};

#[tokio::test]
async fn test_metrics_collection() {
    // 创建监控配置
    let monitoring_config = MonitoringConfig {
        enabled: true,
        metrics_port: 0, // 使用随机端口
        metrics_interval_seconds: 1,
        health_check_port: 0,
        alerts: AlertConfig {
            enabled: false,
            webhook_url: None,
            thresholds: AlertThresholds {
                cpu_usage_threshold: 80.0,
                memory_usage_threshold: 85.0,
                disk_usage_threshold: 90.0,
                error_rate_threshold: 5.0,
            },
        },
    };

    // 创建监控管理器
    let monitoring_manager = MonitoringManager::new(monitoring_config, None).await.unwrap();
    
    // 启动监控
    monitoring_manager.start().await.unwrap();
    
    // 等待一段时间让指标收集
    sleep(Duration::from_secs(2)).await;
    
    // 获取指标
    let metrics = monitoring_manager.get_current_metrics().await.unwrap();
    assert!(!metrics.is_empty());
    
    // 停止监控
    monitoring_manager.stop().await.unwrap();
}

#[tokio::test]
async fn test_alert_system() {
    // 创建监控配置，启用告警
    let monitoring_config = MonitoringConfig {
        enabled: true,
        metrics_port: 0,
        metrics_interval_seconds: 1,
        health_check_port: 0,
        alerts: AlertConfig {
            enabled: true,
            webhook_url: None, // 不使用webhook，只测试告警逻辑
            thresholds: AlertThresholds {
                cpu_usage_threshold: 1.0, // 设置很低的阈值，容易触发
                memory_usage_threshold: 1.0,
                disk_usage_threshold: 1.0,
                error_rate_threshold: 1.0,
            },
        },
    };

    // 创建监控管理器
    let monitoring_manager = MonitoringManager::new(monitoring_config, None).await.unwrap();
    
    // 启动监控
    monitoring_manager.start().await.unwrap();
    
    // 等待告警检查
    sleep(Duration::from_secs(3)).await;
    
    // 获取活跃告警 - 通过告警管理器
    let alerts = monitoring_manager.alert_manager().get_active_alerts().await;
    // 由于阈值设置很低，应该有告警触发
    assert!(!alerts.is_empty());
    
    // 停止监控
    monitoring_manager.stop().await.unwrap();
}

#[tokio::test]
async fn test_log_system() {
    // 创建临时日志文件路径
    let temp_dir = std::env::temp_dir();
    let log_path = temp_dir.join("kvm_tunnel_test.log");
    
    // 创建日志配置
    let logging_config = LoggingConfig {
        level: "debug".to_string(),
        format: LogFormat::Text,
        targets: vec![],
        file: LogFileConfig {
            path: log_path.to_string_lossy().to_string(),
            max_size_mb: 10,
            max_files: 5,
            compress: false,
        },
        structured_fields: vec![],
    };

    // 创建日志管理器
    let log_manager = LogManager::new(logging_config).await.unwrap();
    
    // 启动日志管理器
    log_manager.start().await.unwrap();
    
    // 写入一些日志条目
    let log_entry = LogEntry {
        timestamp: Utc::now(),
        level: LogLevel::Info,
        target: "test".to_string(),
        message: "测试日志消息".to_string(),
        file: Some("test.rs".to_string()),
        line: Some(100),
        fields: std::collections::HashMap::new(),
    };
    
    log_manager.write_log_entry(log_entry).await.unwrap();
    
    // 等待日志写入
    sleep(Duration::from_millis(100)).await;
    
    // 检查日志文件是否存在
    assert!(log_path.exists());
    
    // 读取日志文件内容
    let log_content = tokio::fs::read_to_string(&log_path).await.unwrap();
    assert!(log_content.contains("测试日志消息"));
    
    // 获取日志统计
    let stats = log_manager.get_log_statistics().await;
    assert_eq!(stats.total_entries, 1);
    assert_eq!(stats.info_count, 1);
    
    // 停止日志管理器
    log_manager.stop().await.unwrap();
    
    // 清理测试文件
    if log_path.exists() {
        let _ = std::fs::remove_file(&log_path);
    }
}

#[tokio::test]
async fn test_integrated_monitoring_and_logging() {
    // 创建临时日志文件路径
    let temp_dir = std::env::temp_dir();
    let log_path = temp_dir.join("kvm_tunnel_integrated_test.log");
    
    // 创建监控配置
    let monitoring_config = MonitoringConfig {
        enabled: true,
        metrics_port: 0,
        metrics_interval_seconds: 1,
        health_check_port: 0,
        alerts: AlertConfig {
            enabled: true,
            webhook_url: None,
            thresholds: AlertThresholds {
                cpu_usage_threshold: 80.0,
                memory_usage_threshold: 85.0,
                disk_usage_threshold: 90.0,
                error_rate_threshold: 5.0,
            },
        },
    };
    
    // 创建日志配置
    let logging_config = LoggingConfig {
        level: "info".to_string(),
        format: LogFormat::Json,
        targets: vec![],
        file: LogFileConfig {
            path: log_path.to_string_lossy().to_string(),
            max_size_mb: 10,
            max_files: 5,
            compress: false,
        },
        structured_fields: vec![],
    };

    // 创建集成的监控管理器
    let monitoring_manager = MonitoringManager::new(monitoring_config, Some(logging_config)).await.unwrap();
    
    // 启动监控和日志
    monitoring_manager.start().await.unwrap();
    
    // 等待系统运行
    sleep(Duration::from_secs(2)).await;
    
    // 验证指标收集
    let metrics = monitoring_manager.get_current_metrics().await.unwrap();
    assert!(!metrics.is_empty());
    
    // 验证告警系统
    let alerts = monitoring_manager.alert_manager().get_active_alerts().await;
    // 可能有告警，也可能没有，取决于系统状态
    
    // 停止监控
    monitoring_manager.stop().await.unwrap();
    
    // 清理测试文件
    if log_path.exists() {
        let _ = std::fs::remove_file(&log_path);
    }
}

#[tokio::test]
async fn test_log_rotation() {
    // 创建临时日志文件路径
    let temp_dir = std::env::temp_dir();
    let log_path = temp_dir.join("kvm_tunnel_rotation_test.log");
    
    // 创建日志配置，设置很小的文件大小以触发轮转
    let logging_config = LoggingConfig {
        level: "debug".to_string(),
        format: LogFormat::Text,
        targets: vec![],
        file: LogFileConfig {
            path: log_path.to_string_lossy().to_string(),
            max_size_mb: 1, // 1MB，很容易达到
            max_files: 3,
            compress: false,
        },
        structured_fields: vec![],
    };

    // 创建日志管理器
    let log_manager = LogManager::new(logging_config).await.unwrap();
    
    // 启动日志管理器
    log_manager.start().await.unwrap();
    
    // 写入大量日志条目以触发轮转
    for i in 0..1000 {
        let log_entry = LogEntry {
            timestamp: Utc::now(),
            level: LogLevel::Debug,
            target: "rotation_test".to_string(),
            message: format!("这是第{}条测试日志消息，用于测试日志轮转功能", i),
            file: Some("rotation_test.rs".to_string()),
            line: Some(i as u32),
            fields: std::collections::HashMap::new(),
        };
        
        log_manager.write_log_entry(log_entry).await.unwrap();
        
        // 每100条消息后稍微等待
        if i % 100 == 0 {
            sleep(Duration::from_millis(10)).await;
        }
    }
    
    // 等待日志写入完成
    sleep(Duration::from_millis(500)).await;
    
    // 检查日志文件是否存在
    assert!(log_path.exists());
    
    // 获取日志统计
    let stats = log_manager.get_log_statistics().await;
    assert_eq!(stats.total_entries, 1000);
    assert_eq!(stats.debug_count, 1000);
    
    // 停止日志管理器
    log_manager.stop().await.unwrap();
    
    // 清理测试文件
    if log_path.exists() {
        let _ = std::fs::remove_file(&log_path);
    }
    
    // 清理可能的轮转文件
    if let Some(parent) = log_path.parent() {
        if let Ok(entries) = std::fs::read_dir(parent) {
            for entry in entries.flatten() {
                let path = entry.path();
                if let Some(name) = path.file_name().and_then(|n| n.to_str()) {
                    if name.starts_with("kvm_tunnel_rotation_test.log") {
                        let _ = std::fs::remove_file(&path);
                    }
                }
            }
        }
    }
}
