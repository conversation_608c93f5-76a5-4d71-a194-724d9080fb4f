// 安全审计系统测试
// 测试安全事件记录、存储和分析功能

use std::net::IpAddr;
use std::str::FromStr;
use chrono::Utc;

use kvm_tunnel::security::audit::{
    SecurityAuditor, SecurityEvent, SecurityEventType, CallerType, CallerInfo, 
    SecurityEventResult
};

/// 测试安全审计器的基本功能
#[tokio::test]
async fn test_security_auditor_basic_functionality() {
    // 创建安全审计器
    let auditor = SecurityAuditor::new().await.unwrap();
    
    // 创建测试安全事件
    let event = SecurityEvent {
        event_id: "test-event-001".to_string(),
        event_type: SecurityEventType::CertificateVerification,
        timestamp: Utc::now(),
        caller_info: CallerInfo {
            caller_id: "test-client".to_string(),
            caller_type: CallerType::ForwarderNode,
            source_ip: IpAddr::from_str("*************").unwrap(),
            certificate_fingerprint: Some("test-fingerprint".to_string()),
        },
        api_path: "/api/auth/login".to_string(),
        action: "login".to_string(),
        result: SecurityEventResult::Success,
        details: serde_json::json!({"message": "用户登录成功"}),
    };
    
    // 记录安全事件
    auditor.record_event(event.clone()).await.unwrap();
    
    // 验证事件被正确记录
    let recent_events = auditor.get_recent_events(10).await;
    assert_eq!(recent_events.len(), 1);
    assert_eq!(recent_events[0].event_id, "test-event-001");
    assert_eq!(recent_events[0].event_type, SecurityEventType::CertificateVerification);
}

/// 测试多种类型的安全事件
#[tokio::test]
async fn test_multiple_security_event_types() {
    let auditor = SecurityAuditor::new().await.unwrap();
    
    // 证书验证事件
    let cert_event = SecurityEvent {
        event_id: "cert-001".to_string(),
        event_type: SecurityEventType::CertificateVerification,
        timestamp: Utc::now(),
        caller_info: CallerInfo {
            caller_id: "node1".to_string(),
            caller_type: CallerType::ForwarderNode,
            source_ip: IpAddr::from_str("********").unwrap(),
            certificate_fingerprint: Some("cert-fingerprint".to_string()),
        },
        api_path: "/api/internal/verify".to_string(),
        action: "certificate_verification".to_string(),
        result: SecurityEventResult::Success,
        details: serde_json::json!({"message": "证书验证成功"}),
    };
    
    // 访问拒绝事件
    let access_denied_event = SecurityEvent {
        event_id: "access-001".to_string(),
        event_type: SecurityEventType::AccessDenied,
        timestamp: Utc::now(),
        caller_info: CallerInfo {
            caller_id: "unauthorized-client".to_string(),
            caller_type: CallerType::ManagementServer,
            source_ip: IpAddr::from_str("**********").unwrap(),
            certificate_fingerprint: None,
        },
        api_path: "/api/admin/users".to_string(),
        action: "access_attempt".to_string(),
        result: SecurityEventResult::Failure("权限不足".to_string()),
        details: serde_json::json!({"reason": "insufficient_permissions"}),
    };
    
    // 记录所有事件
    auditor.record_event(cert_event).await.unwrap();
    auditor.record_event(access_denied_event).await.unwrap();
    
    // 验证所有事件都被记录
    let events = auditor.get_recent_events(10).await;
    assert_eq!(events.len(), 2);
    
    // 验证事件类型分布
    let cert_events: Vec<_> = events.iter()
        .filter(|e| e.event_type == SecurityEventType::CertificateVerification)
        .collect();
    let access_events: Vec<_> = events.iter()
        .filter(|e| e.event_type == SecurityEventType::AccessDenied)
        .collect();
    
    assert_eq!(cert_events.len(), 1);
    assert_eq!(access_events.len(), 1);
}

/// 测试事件查询功能
#[tokio::test]
async fn test_event_query() {
    let auditor = SecurityAuditor::new().await.unwrap();
    
    let now = Utc::now();
    
    // 创建不同时间的事件
    let old_event = SecurityEvent {
        event_id: "old-event".to_string(),
        event_type: SecurityEventType::CertificateVerification,
        timestamp: now - chrono::Duration::hours(2),
        caller_info: CallerInfo {
            caller_id: "old-user".to_string(),
            caller_type: CallerType::ForwarderNode,
            source_ip: IpAddr::from_str("***********").unwrap(),
            certificate_fingerprint: Some("old-cert".to_string()),
        },
        api_path: "/api/old".to_string(),
        action: "old_action".to_string(),
        result: SecurityEventResult::Success,
        details: serde_json::json!({"message": "旧事件"}),
    };
    
    let recent_event = SecurityEvent {
        event_id: "recent-event".to_string(),
        event_type: SecurityEventType::AccessDenied,
        timestamp: now - chrono::Duration::minutes(30),
        caller_info: CallerInfo {
            caller_id: "recent-user".to_string(),
            caller_type: CallerType::ManagementServer,
            source_ip: IpAddr::from_str("**********").unwrap(),
            certificate_fingerprint: None,
        },
        api_path: "/api/data".to_string(),
        action: "recent_action".to_string(),
        result: SecurityEventResult::Failure("访问被拒绝".to_string()),
        details: serde_json::json!({"message": "最近事件"}),
    };
    
    // 记录事件
    auditor.record_event(old_event).await.unwrap();
    auditor.record_event(recent_event).await.unwrap();
    
    // 获取所有事件
    let all_events = auditor.get_recent_events(10).await;
    assert_eq!(all_events.len(), 2);
    
    // 验证事件按时间排序（最新的在前）
    assert_eq!(all_events[0].event_id, "recent-event");
    assert_eq!(all_events[1].event_id, "old-event");
}

/// 测试事件统计功能
#[tokio::test]
async fn test_event_statistics() {
    let auditor = SecurityAuditor::new().await.unwrap();
    
    // 创建多个不同类型的事件
    for i in 0..3 {
        let cert_event = SecurityEvent {
            event_id: format!("cert-{}", i),
            event_type: SecurityEventType::CertificateVerification,
            timestamp: Utc::now(),
            caller_info: CallerInfo {
                caller_id: format!("node-{}", i),
                caller_type: CallerType::ForwarderNode,
                source_ip: IpAddr::from_str("********").unwrap(),
                certificate_fingerprint: Some(format!("cert-{}", i)),
            },
            api_path: "/api/verify".to_string(),
            action: "verify".to_string(),
            result: SecurityEventResult::Success,
            details: serde_json::json!({"node": i}),
        };
        auditor.record_event(cert_event).await.unwrap();
    }
    
    for i in 0..2 {
        let access_event = SecurityEvent {
            event_id: format!("access-{}", i),
            event_type: SecurityEventType::AccessDenied,
            timestamp: Utc::now(),
            caller_info: CallerInfo {
                caller_id: format!("user-{}", i),
                caller_type: CallerType::ManagementServer,
                source_ip: IpAddr::from_str("**********").unwrap(),
                certificate_fingerprint: None,
            },
            api_path: "/api/admin".to_string(),
            action: "access".to_string(),
            result: SecurityEventResult::Failure("拒绝访问".to_string()),
            details: serde_json::json!({"user": i}),
        };
        auditor.record_event(access_event).await.unwrap();
    }
    
    // 验证事件总数
    let all_events = auditor.get_recent_events(10).await;
    assert_eq!(all_events.len(), 5);
    
    // 验证事件类型分布
    let cert_count = all_events.iter()
        .filter(|e| e.event_type == SecurityEventType::CertificateVerification)
        .count();
    let access_count = all_events.iter()
        .filter(|e| e.event_type == SecurityEventType::AccessDenied)
        .count();
    
    assert_eq!(cert_count, 3);
    assert_eq!(access_count, 2);
}

/// 测试事件清理功能
#[tokio::test]
async fn test_event_cleanup() {
    let auditor = SecurityAuditor::new().await.unwrap();
    
    // 创建大量事件
    for i in 0..15 {
        let event = SecurityEvent {
            event_id: format!("cleanup-test-{:03}", i),
            event_type: SecurityEventType::CertificateVerification,
            timestamp: Utc::now(),
            caller_info: CallerInfo {
                caller_id: format!("test-user-{}", i),
                caller_type: CallerType::ForwarderNode,
                source_ip: IpAddr::from_str("*************").unwrap(),
                certificate_fingerprint: Some(format!("cert-{}", i)),
            },
            api_path: "/api/test".to_string(),
            action: "test".to_string(),
            result: SecurityEventResult::Success,
            details: serde_json::json!({"test_id": i}),
        };
        auditor.record_event(event).await.unwrap();
    }
    
    // 验证所有事件都被记录
    let all_events = auditor.get_recent_events(20).await;
    assert_eq!(all_events.len(), 15);
    
    // 只获取最近的10个事件
    let recent_events = auditor.get_recent_events(10).await;
    assert_eq!(recent_events.len(), 10);
    
    // 验证返回的是最新的事件
    assert_eq!(recent_events[0].event_id, "cleanup-test-014");
    assert_eq!(recent_events[9].event_id, "cleanup-test-005");
}
