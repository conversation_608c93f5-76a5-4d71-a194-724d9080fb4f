// 集成测试
// 测试转发节点的主要功能

use std::sync::Arc;
use tokio::time::{sleep, Duration};
use kvm_tunnel::{
    config::ForwarderConfig,
    core::ForwarderNode,
    types::{NodeStatus, NodeType},
};

/// 测试转发节点的基本启动和停止
#[tokio::test]
async fn test_forwarder_node_basic_lifecycle() {
    // 创建测试配置
    let config = create_test_config().await;
    
    // 创建转发节点 - 如果因为证书问题失败，跳过测试
    let forwarder = match ForwarderNode::new(config).await {
        Ok(node) => Arc::new(node),
        Err(e) => {
            println!("跳过测试：无法创建转发节点: {}", e);
            return;
        }
    };
    
    // 启动转发节点
    forwarder.start().await.expect("启动转发节点失败");
    
    // 等待一小段时间确保启动完成
    sleep(Duration::from_millis(100)).await;
    
    // 检查节点状态
    let status = forwarder.get_status().await;
    assert!(matches!(status, NodeStatus::Online | NodeStatus::Starting));
    
    // 获取节点信息
    let node_info = forwarder.get_node_info().await;
    assert_eq!(node_info.node_type, NodeType::Forwarder);
    assert!(!node_info.node_id.is_empty());
    
    // 停止转发节点
    forwarder.stop().await.expect("停止转发节点失败");
    
    // 检查节点状态
    let status = forwarder.get_status().await;
    assert_eq!(status, NodeStatus::Offline);
}

/// 测试配置加载
#[tokio::test]
async fn test_config_loading() {
    let config = create_test_config().await;
    
    // 验证配置的基本字段
    assert!(!config.node.node_id.is_empty());
    assert_eq!(config.node.node_type, NodeType::Forwarder);
    assert!(config.node.listen_port > 0);
    assert!(config.network.api_port > 0);
    assert!(config.network.websocket_port > 0);
    assert!(config.node.capabilities.max_connections > 0);
}

/// 测试健康检查
#[tokio::test]
async fn test_health_check() {
    let config = create_test_config().await;
    let forwarder = match ForwarderNode::new(config).await {
        Ok(node) => Arc::new(node),
        Err(e) => {
            println!("跳过测试：无法创建转发节点: {}", e);
            return;
        }
    };
    
    // 启动转发节点
    forwarder.start().await.expect("启动转发节点失败");
    
    // 等待启动完成
    sleep(Duration::from_millis(100)).await;
    
    // 获取健康状态
    let health = forwarder.get_health().await;
    
    // 验证健康状态的基本字段
    assert!(health.last_check <= chrono::Utc::now());
    
    // 停止转发节点
    forwarder.stop().await.expect("停止转发节点失败");
}

/// 测试系统统计
#[tokio::test]
async fn test_system_stats() {
    let config = create_test_config().await;
    let forwarder = match ForwarderNode::new(config).await {
        Ok(node) => Arc::new(node),
        Err(e) => {
            println!("跳过测试：无法创建转发节点: {}", e);
            return;
        }
    };
    
    // 启动转发节点
    forwarder.start().await.expect("启动转发节点失败");
    
    // 等待启动完成
    sleep(Duration::from_millis(100)).await;
    
    // 获取系统统计
    let stats = forwarder.get_stats().await;
    
    // 验证统计信息的基本字段
    assert!(stats.uptime_seconds >= 0);
    assert!(stats.memory_usage_percent >= 0.0);
    assert!(stats.disk_usage_percent >= 0.0);
    
    // 停止转发节点
    forwarder.stop().await.expect("停止转发节点失败");
}

/// 创建测试配置
async fn create_test_config() -> ForwarderConfig {
    use kvm_tunnel::{
        config::*,
        types::{NodeCapabilities, NodeType},
    };
    
    ForwarderConfig {
        node: NodeConfig {
            node_id: "test-forwarder-001".to_string(),
            node_type: NodeType::Forwarder,
            listen_ip: "127.0.0.1".to_string(),
            listen_port: 18080,
            region: "test".to_string(),
            capabilities: NodeCapabilities {
                max_connections: 100,
                max_bandwidth_mbps: 100,
                supported_codecs: vec!["H264".to_string()],
                supported_protocols: vec!["TCP".to_string(), "UDP".to_string()],
                supports_mediasoup: true,
                supports_tcp_tunnel: true,
                supports_udp_tunnel: true,
                supports_multicast: true,
            },
            management_server_url: "http://localhost:8000".to_string(),
            heartbeat_interval_seconds: 30,
            session_timeout_seconds: 300,
        },
        network: NetworkConfig {
            bind_address: "127.0.0.1".to_string(),
            api_port: 18080,
            websocket_port: 18081,
            max_connections: 100,
            connection_timeout_seconds: 30,
            read_timeout_seconds: 30,
            write_timeout_seconds: 30,
            tcp_keepalive: TcpKeepaliveConfig {
                enabled: true,
                keepalive_time_seconds: 600,
                keepalive_interval_seconds: 60,
                keepalive_retries: 3,
            },
            port_ranges: PortRangeConfig {
                mediasoup_port_range: (20000, 21000),
                tcp_tunnel_port_range: (21001, 22000),
                udp_tunnel_port_range: (22001, 23000),
                multicast_port_range: (23001, 24000),
            },
        },
        mediasoup: MediaSoupConfig {
            worker_count: 1,
            max_connections_per_worker: 50,
            rtc_port_range: (20000, 21000),
            log_level: "warn".to_string(),
            log_tags: vec!["info".to_string()],
            dtls_cert_file: None,
            dtls_key_file: None,
        },
        security: SecurityConfig {
            node_certificate_path: "/tmp/test-cert.pem".to_string(),
            node_private_key_path: "/tmp/test-key.pem".to_string(),
            ca_certificate_path: "/tmp/test-ca.pem".to_string(),
            certificate_rotation_days: 90,
            certificate_check_interval_hours: 1,
            trusted_servers: vec![],
            api_permissions: ApiPermissionConfig {
                management_only_apis: vec![],
                inter_node_only_apis: vec![],
                both_callable_apis: vec!["GET /health".to_string()],
            },
            rate_limits: RateLimitConfig {
                enabled: false,
                max_requests_per_minute: 100,
                burst_size: 10,
                window_size_seconds: 60,
            },
        },
        logging: LoggingConfig {
            level: "info".to_string(),
            format: LogFormat::Text,
            targets: vec![LogTarget::Stdout],
            file: LogFileConfig {
                path: "/tmp/test.log".to_string(),
                max_size_mb: 10,
                max_files: 5,
                compress: false,
            },
            structured_fields: vec![],
        },
        monitoring: MonitoringConfig {
            enabled: false,
            metrics_port: 19090,
            metrics_interval_seconds: 15,
            health_check_port: 19082,
            alerts: AlertConfig {
                enabled: false,
                webhook_url: None,
                thresholds: AlertThresholds {
                    cpu_usage_threshold: 80.0,
                    memory_usage_threshold: 85.0,
                    disk_usage_threshold: 90.0,
                    error_rate_threshold: 5.0,
                },
            },
        },
        storage: StorageConfig {
            memory: MemoryStorageConfig {
                max_sessions: 1000,
                max_transports: 5000,
                cleanup_interval_seconds: 300,
            },
            disk: DiskStorageConfig {
                base_path: "/tmp/kvm_tunnel_test".to_string(),
                max_log_size_mb: 10,
                log_retention_days: 7,
                sync_interval_seconds: 60,
            },
        },
    }
}
