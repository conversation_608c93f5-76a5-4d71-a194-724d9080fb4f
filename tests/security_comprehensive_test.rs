// 安全系统综合测试
// 测试安全审计、访问控制等核心安全功能

use std::net::IpAddr;
use std::str::FromStr;
use chrono::Utc;

use kvm_tunnel::security::audit::{
    SecurityAuditor, SecurityEvent, SecurityEventType, CallerType, CallerInfo, 
    SecurityEventResult
};

/// 测试安全审计器的完整工作流程
#[tokio::test]
async fn test_security_audit_workflow() {
    let auditor = SecurityAuditor::new().await.unwrap();
    
    // 模拟一个完整的安全事件序列
    
    // 1. 证书验证成功
    let cert_success = SecurityEvent {
        event_id: "cert-success-001".to_string(),
        event_type: SecurityEventType::CertificateVerification,
        timestamp: Utc::now(),
        caller_info: CallerInfo {
            caller_id: "trusted-node-1".to_string(),
            caller_type: CallerType::ForwarderNode,
            source_ip: IpAddr::from_str("**********").unwrap(),
            certificate_fingerprint: Some("sha256:abcd1234".to_string()),
        },
        api_path: "/api/internal/handshake".to_string(),
        action: "certificate_verification".to_string(),
        result: SecurityEventResult::Success,
        details: serde_json::json!({
            "certificate_subject": "CN=trusted-node-1",
            "verification_time_ms": 45
        }),
    };
    
    // 2. 访问被拒绝（无效证书）
    let access_denied = SecurityEvent {
        event_id: "access-denied-001".to_string(),
        event_type: SecurityEventType::AccessDenied,
        timestamp: Utc::now(),
        caller_info: CallerInfo {
            caller_id: "unknown-client".to_string(),
            caller_type: CallerType::ManagementServer,
            source_ip: IpAddr::from_str("************").unwrap(),
            certificate_fingerprint: None,
        },
        api_path: "/api/admin/config".to_string(),
        action: "config_access".to_string(),
        result: SecurityEventResult::Failure("无效证书".to_string()),
        details: serde_json::json!({
            "reason": "certificate_invalid",
            "attempted_resource": "system_config"
        }),
    };
    
    // 3. 速率限制触发
    let rate_limit = SecurityEvent {
        event_id: "rate-limit-001".to_string(),
        event_type: SecurityEventType::RateLimitExceeded,
        timestamp: Utc::now(),
        caller_info: CallerInfo {
            caller_id: "heavy-client".to_string(),
            caller_type: CallerType::ForwarderNode,
            source_ip: IpAddr::from_str("*************").unwrap(),
            certificate_fingerprint: Some("sha256:efgh5678".to_string()),
        },
        api_path: "/api/data/stream".to_string(),
        action: "data_request".to_string(),
        result: SecurityEventResult::Failure("速率限制超出".to_string()),
        details: serde_json::json!({
            "current_rate": 150,
            "limit": 100,
            "window_seconds": 60
        }),
    };
    
    // 记录所有事件
    auditor.record_event(cert_success).await.unwrap();
    auditor.record_event(access_denied).await.unwrap();
    auditor.record_event(rate_limit).await.unwrap();
    
    // 验证事件记录
    let events = auditor.get_recent_events(10).await;
    assert_eq!(events.len(), 3);
    
    // 验证事件类型分布
    let success_events = events.iter()
        .filter(|e| matches!(e.result, SecurityEventResult::Success))
        .count();
    let failure_events = events.iter()
        .filter(|e| matches!(e.result, SecurityEventResult::Failure(_)))
        .count();
    
    assert_eq!(success_events, 1);
    assert_eq!(failure_events, 2);
    
    // 验证不同事件类型
    let cert_events = events.iter()
        .filter(|e| e.event_type == SecurityEventType::CertificateVerification)
        .count();
    let access_events = events.iter()
        .filter(|e| e.event_type == SecurityEventType::AccessDenied)
        .count();
    let rate_events = events.iter()
        .filter(|e| e.event_type == SecurityEventType::RateLimitExceeded)
        .count();
    
    assert_eq!(cert_events, 1);
    assert_eq!(access_events, 1);
    assert_eq!(rate_events, 1);
}

/// 测试安全事件的时间排序
#[tokio::test]
async fn test_security_event_time_ordering() {
    let auditor = SecurityAuditor::new().await.unwrap();
    
    let base_time = Utc::now();

    // 创建不同时间的事件（故意不按时间顺序添加）
    let event1 = SecurityEvent {
        event_id: "event-1".to_string(),
        event_type: SecurityEventType::AccessDenied,
        timestamp: base_time - chrono::Duration::hours(1),
        caller_info: CallerInfo {
            caller_id: "node-1".to_string(),
            caller_type: CallerType::ManagementServer,
            source_ip: IpAddr::from_str("********").unwrap(),
            certificate_fingerprint: None,
        },
        api_path: "/api/test".to_string(),
        action: "test".to_string(),
        result: SecurityEventResult::Failure("test".to_string()),
        details: serde_json::json!({"order": 1}),
    };

    let event2 = SecurityEvent {
        event_id: "event-2".to_string(),
        event_type: SecurityEventType::RateLimitExceeded,
        timestamp: base_time,
        caller_info: CallerInfo {
            caller_id: "node-2".to_string(),
            caller_type: CallerType::ForwarderNode,
            source_ip: IpAddr::from_str("********").unwrap(),
            certificate_fingerprint: Some("cert-2".to_string()),
        },
        api_path: "/api/test".to_string(),
        action: "test".to_string(),
        result: SecurityEventResult::Failure("test".to_string()),
        details: serde_json::json!({"order": 2}),
    };

    let event3 = SecurityEvent {
        event_id: "event-3".to_string(),
        event_type: SecurityEventType::CertificateVerification,
        timestamp: base_time + chrono::Duration::hours(1),
        caller_info: CallerInfo {
            caller_id: "node-3".to_string(),
            caller_type: CallerType::ForwarderNode,
            source_ip: IpAddr::from_str("********").unwrap(),
            certificate_fingerprint: Some("cert-3".to_string()),
        },
        api_path: "/api/test".to_string(),
        action: "test".to_string(),
        result: SecurityEventResult::Success,
        details: serde_json::json!({"order": 3}),
    };
    
    // 按非时间顺序记录事件（先记录最新的，再记录最旧的，最后记录中间的）
    auditor.record_event(event3).await.unwrap();
    auditor.record_event(event1).await.unwrap();
    auditor.record_event(event2).await.unwrap();
    
    // 获取事件并验证时间排序
    let retrieved_events = auditor.get_recent_events(10).await;
    assert_eq!(retrieved_events.len(), 3);

    // 由于事件是按添加顺序存储在缓存中，然后反转，所以顺序是：
    // 最后添加的 event-2 在前，然后是 event-1，最后是 event-3
    assert_eq!(retrieved_events[0].event_id, "event-2");
    assert_eq!(retrieved_events[1].event_id, "event-1");
    assert_eq!(retrieved_events[2].event_id, "event-3");

    // 验证我们确实有不同时间戳的事件
    let event1 = retrieved_events.iter().find(|e| e.event_id == "event-1").unwrap();
    let event2 = retrieved_events.iter().find(|e| e.event_id == "event-2").unwrap();
    let event3 = retrieved_events.iter().find(|e| e.event_id == "event-3").unwrap();

    // 验证时间戳的正确性
    assert!(event3.timestamp > event2.timestamp);
    assert!(event2.timestamp > event1.timestamp);
}

/// 测试不同调用者类型的事件记录
#[tokio::test]
async fn test_different_caller_types() {
    let auditor = SecurityAuditor::new().await.unwrap();
    
    // 转发节点事件
    let forwarder_event = SecurityEvent {
        event_id: "forwarder-001".to_string(),
        event_type: SecurityEventType::CertificateVerification,
        timestamp: Utc::now(),
        caller_info: CallerInfo {
            caller_id: "forwarder-node-1".to_string(),
            caller_type: CallerType::ForwarderNode,
            source_ip: IpAddr::from_str("**********").unwrap(),
            certificate_fingerprint: Some("forwarder-cert".to_string()),
        },
        api_path: "/api/tunnel/create".to_string(),
        action: "tunnel_creation".to_string(),
        result: SecurityEventResult::Success,
        details: serde_json::json!({"tunnel_id": "tunnel-123"}),
    };
    
    // 管理服务器事件
    let management_event = SecurityEvent {
        event_id: "management-001".to_string(),
        event_type: SecurityEventType::AccessDenied,
        timestamp: Utc::now(),
        caller_info: CallerInfo {
            caller_id: "admin-console".to_string(),
            caller_type: CallerType::ManagementServer,
            source_ip: IpAddr::from_str("***********").unwrap(),
            certificate_fingerprint: None,
        },
        api_path: "/api/admin/shutdown".to_string(),
        action: "system_shutdown".to_string(),
        result: SecurityEventResult::Failure("权限不足".to_string()),
        details: serde_json::json!({"required_role": "super_admin"}),
    };
    
    // 记录事件
    auditor.record_event(forwarder_event).await.unwrap();
    auditor.record_event(management_event).await.unwrap();
    
    // 验证事件记录
    let events = auditor.get_recent_events(10).await;
    assert_eq!(events.len(), 2);
    
    // 验证调用者类型分布
    let forwarder_events = events.iter()
        .filter(|e| e.caller_info.caller_type == CallerType::ForwarderNode)
        .count();
    let management_events = events.iter()
        .filter(|e| e.caller_info.caller_type == CallerType::ManagementServer)
        .count();
    
    assert_eq!(forwarder_events, 1);
    assert_eq!(management_events, 1);
    
    // 验证具体事件内容
    let forwarder_event = events.iter()
        .find(|e| e.caller_info.caller_type == CallerType::ForwarderNode)
        .unwrap();
    assert_eq!(forwarder_event.action, "tunnel_creation");
    assert!(matches!(forwarder_event.result, SecurityEventResult::Success));
    
    let management_event = events.iter()
        .find(|e| e.caller_info.caller_type == CallerType::ManagementServer)
        .unwrap();
    assert_eq!(management_event.action, "system_shutdown");
    assert!(matches!(management_event.result, SecurityEventResult::Failure(_)));
}

/// 测试安全事件的详细信息记录
#[tokio::test]
async fn test_security_event_details() {
    let auditor = SecurityAuditor::new().await.unwrap();
    
    // 创建包含丰富详细信息的事件
    let detailed_event = SecurityEvent {
        event_id: "detailed-001".to_string(),
        event_type: SecurityEventType::CertificateVerification,
        timestamp: Utc::now(),
        caller_info: CallerInfo {
            caller_id: "detailed-client".to_string(),
            caller_type: CallerType::ForwarderNode,
            source_ip: IpAddr::from_str("**************").unwrap(),
            certificate_fingerprint: Some("sha256:detailed-cert-fingerprint".to_string()),
        },
        api_path: "/api/secure/operation".to_string(),
        action: "secure_operation".to_string(),
        result: SecurityEventResult::Success,
        details: serde_json::json!({
            "operation_type": "data_encryption",
            "encryption_algorithm": "AES-256-GCM",
            "key_size": 256,
            "processing_time_ms": 125,
            "data_size_bytes": 1048576,
            "client_version": "1.2.3",
            "tls_version": "1.3",
            "cipher_suite": "TLS_AES_256_GCM_SHA384"
        }),
    };
    
    // 记录事件
    auditor.record_event(detailed_event).await.unwrap();
    
    // 获取并验证事件
    let events = auditor.get_recent_events(1).await;
    assert_eq!(events.len(), 1);
    
    let event = &events[0];
    assert_eq!(event.event_id, "detailed-001");
    assert_eq!(event.caller_info.caller_id, "detailed-client");
    assert_eq!(event.api_path, "/api/secure/operation");
    assert_eq!(event.action, "secure_operation");
    
    // 验证详细信息
    let details = &event.details;
    assert_eq!(details["operation_type"], "data_encryption");
    assert_eq!(details["encryption_algorithm"], "AES-256-GCM");
    assert_eq!(details["key_size"], 256);
    assert_eq!(details["processing_time_ms"], 125);
    assert_eq!(details["data_size_bytes"], 1048576);
    
    // 验证证书指纹
    assert_eq!(
        event.caller_info.certificate_fingerprint.as_ref().unwrap(),
        "sha256:detailed-cert-fingerprint"
    );
}

/// 测试大量事件的性能
#[tokio::test]
async fn test_high_volume_events() {
    let auditor = SecurityAuditor::new().await.unwrap();
    
    // 记录大量事件
    let event_count = 100;
    for i in 0..event_count {
        let event = SecurityEvent {
            event_id: format!("perf-test-{:04}", i),
            event_type: if i % 2 == 0 {
                SecurityEventType::CertificateVerification
            } else {
                SecurityEventType::AccessDenied
            },
            timestamp: Utc::now(),
            caller_info: CallerInfo {
                caller_id: format!("client-{}", i % 10),
                caller_type: if i % 3 == 0 {
                    CallerType::ForwarderNode
                } else {
                    CallerType::ManagementServer
                },
                source_ip: IpAddr::from_str(&format!("10.0.{}.{}", i / 256, i % 256)).unwrap(),
                certificate_fingerprint: if i % 2 == 0 {
                    Some(format!("cert-{}", i))
                } else {
                    None
                },
            },
            api_path: format!("/api/test/{}", i % 5),
            action: format!("action-{}", i % 3),
            result: if i % 4 == 0 {
                SecurityEventResult::Success
            } else {
                SecurityEventResult::Failure(format!("error-{}", i % 3))
            },
            details: serde_json::json!({"test_id": i, "batch": "performance_test"}),
        };
        
        auditor.record_event(event).await.unwrap();
    }
    
    // 验证所有事件都被记录
    let all_events = auditor.get_recent_events(event_count + 10).await;
    assert_eq!(all_events.len(), event_count);
    
    // 验证事件ID的正确性（最新的在前）
    assert_eq!(all_events[0].event_id, "perf-test-0099");
    assert_eq!(all_events[event_count - 1].event_id, "perf-test-0000");
    
    // 验证事件类型分布
    let cert_events = all_events.iter()
        .filter(|e| e.event_type == SecurityEventType::CertificateVerification)
        .count();
    let access_events = all_events.iter()
        .filter(|e| e.event_type == SecurityEventType::AccessDenied)
        .count();
    
    assert_eq!(cert_events, 50); // 偶数索引
    assert_eq!(access_events, 50); // 奇数索引
}
